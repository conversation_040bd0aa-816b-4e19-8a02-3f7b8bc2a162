-- Enable Row Level Security (RLS) and create policies for all tables
-- This fixes the RLS security warnings from Supabase
-- FIXED VERSION: Corrected UUID type casting issues

-- =============================================================================
-- ENABLE RLS ON ALL TABLES
-- =============================================================================

ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Chat" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Message" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Message_v2" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Vote" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Vote_v2" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Document" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Suggestion" ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- USER TABLE POLICIES
-- =============================================================================

-- Users can only see and update their own profile
CREATE POLICY "Users can view own profile" ON "User"
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON "User"
  FOR UPDATE USING (auth.uid() = id);

-- Note: INSERT and DELETE policies for User table are typically handled by auth system
-- If you need them, uncomment and adjust as needed:
-- CREATE POLICY "Users can insert own profile" ON "User"
--   FOR INSERT WITH CHECK (auth.uid() = id);

-- =============================================================================
-- CHAT TABLE POLICIES  
-- =============================================================================

-- Users can see their own chats and public chats
CREATE POLICY "Users can view own chats and public chats" ON "Chat"
  FOR SELECT USING (
    auth.uid() = "userId" 
    OR visibility = 'public'
  );

-- Users can only create chats for themselves
CREATE POLICY "Users can create own chats" ON "Chat"
  FOR INSERT WITH CHECK (auth.uid() = "userId");

-- Users can only update their own chats
CREATE POLICY "Users can update own chats" ON "Chat"
  FOR UPDATE USING (auth.uid() = "userId");

-- Users can only delete their own chats
CREATE POLICY "Users can delete own chats" ON "Chat"
  FOR DELETE USING (auth.uid() = "userId");

-- =============================================================================
-- MESSAGE TABLE POLICIES (DEPRECATED)
-- =============================================================================

-- Users can view messages in chats they own or public chats
CREATE POLICY "Users can view messages in accessible chats" ON "Message"
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can create messages only in their own chats
CREATE POLICY "Users can create messages in own chats" ON "Message"
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message"."chatId" 
      AND "Chat"."userId" = auth.uid()
    )
  );

-- Users can update messages only in their own chats
CREATE POLICY "Users can update messages in own chats" ON "Message"
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message"."chatId" 
      AND "Chat"."userId" = auth.uid()
    )
  );

-- Users can delete messages only in their own chats
CREATE POLICY "Users can delete messages in own chats" ON "Message"
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message"."chatId" 
      AND "Chat"."userId" = auth.uid()
    )
  );

-- =============================================================================
-- MESSAGE_V2 TABLE POLICIES (CURRENT)
-- =============================================================================

-- Users can view messages in chats they own or public chats
CREATE POLICY "Users can view messages v2 in accessible chats" ON "Message_v2"
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message_v2"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can create messages only in their own chats
CREATE POLICY "Users can create messages v2 in own chats" ON "Message_v2"
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message_v2"."chatId" 
      AND "Chat"."userId" = auth.uid()
    )
  );

-- Users can update messages only in their own chats
CREATE POLICY "Users can update messages v2 in own chats" ON "Message_v2"
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message_v2"."chatId" 
      AND "Chat"."userId" = auth.uid()
    )
  );

-- Users can delete messages only in their own chats
CREATE POLICY "Users can delete messages v2 in own chats" ON "Message_v2"
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Message_v2"."chatId" 
      AND "Chat"."userId" = auth.uid()
    )
  );

-- =============================================================================
-- VOTE TABLE POLICIES (DEPRECATED)
-- =============================================================================

-- Users can view votes in chats they own or public chats
CREATE POLICY "Users can view votes in accessible chats" ON "Vote"
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can create votes in any accessible chat
CREATE POLICY "Users can create votes in accessible chats" ON "Vote"
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can update votes in any accessible chat
CREATE POLICY "Users can update votes in accessible chats" ON "Vote"
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can delete votes in any accessible chat
CREATE POLICY "Users can delete votes in accessible chats" ON "Vote"
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- =============================================================================
-- VOTE_V2 TABLE POLICIES (CURRENT)
-- =============================================================================

-- Users can view votes in chats they own or public chats
CREATE POLICY "Users can view votes v2 in accessible chats" ON "Vote_v2"
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote_v2"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can create votes in any accessible chat
CREATE POLICY "Users can create votes v2 in accessible chats" ON "Vote_v2"
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote_v2"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can update votes in any accessible chat
CREATE POLICY "Users can update votes v2 in accessible chats" ON "Vote_v2"
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote_v2"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- Users can delete votes in any accessible chat
CREATE POLICY "Users can delete votes v2 in accessible chats" ON "Vote_v2"
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM "Chat" 
      WHERE "Chat".id = "Vote_v2"."chatId" 
      AND (
        "Chat"."userId" = auth.uid() 
        OR "Chat".visibility = 'public'
      )
    )
  );

-- =============================================================================
-- DOCUMENT TABLE POLICIES
-- =============================================================================

-- Users can only see their own documents
CREATE POLICY "Users can view own documents" ON "Document"
  FOR SELECT USING (auth.uid() = "userId");

-- Users can only create documents for themselves
CREATE POLICY "Users can create own documents" ON "Document"
  FOR INSERT WITH CHECK (auth.uid() = "userId");

-- Users can only update their own documents
CREATE POLICY "Users can update own documents" ON "Document"
  FOR UPDATE USING (auth.uid() = "userId");

-- Users can only delete their own documents
CREATE POLICY "Users can delete own documents" ON "Document"
  FOR DELETE USING (auth.uid() = "userId");

-- =============================================================================
-- SUGGESTION TABLE POLICIES
-- =============================================================================

-- Users can only see their own suggestions
CREATE POLICY "Users can view own suggestions" ON "Suggestion"
  FOR SELECT USING (auth.uid() = "userId");

-- Users can only create suggestions for themselves
CREATE POLICY "Users can create own suggestions" ON "Suggestion"
  FOR INSERT WITH CHECK (auth.uid() = "userId");

-- Users can only update their own suggestions
CREATE POLICY "Users can update own suggestions" ON "Suggestion"
  FOR UPDATE USING (auth.uid() = "userId");

-- Users can only delete their own suggestions
CREATE POLICY "Users can delete own suggestions" ON "Suggestion"
  FOR DELETE USING (auth.uid() = "userId");

-- =============================================================================
-- VERIFICATION QUERIES (Optional - for testing)
-- =============================================================================

-- Run these to verify RLS is enabled:
-- SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public';

-- Run these to verify policies exist:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies WHERE schemaname = 'public'; 