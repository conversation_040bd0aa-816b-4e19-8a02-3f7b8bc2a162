import type { Attachment } from 'ai';
import { useState } from 'react';

import { LoaderIcon, FileIcon } from './icons';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from './ui/sheet';

export const PreviewAttachment = ({
  attachment,
  isUploading = false,
}: {
  attachment: Attachment;
  isUploading?: boolean;
}) => {
  const { name, url, contentType } = attachment;
  const [isOpen, setIsOpen] = useState(false);

  const handlePdfClick = () => {
    if (contentType === 'application/pdf' && url && !isUploading) {
      setIsOpen(true);
    }
  };

  // Extract filename from path and apply truncation
  const actualFileName = name ? name.split('/').pop() : '';

  let fileName = '';

  if (actualFileName === undefined) {
    fileName = 'Bill Attachment';
  } else {
    fileName =  actualFileName.length > 40 ? actualFileName.slice(0, 40) + '...' : actualFileName;
  }

  return (
    <>
      <div data-testid="input-attachment-preview" className="flex flex-col gap-2">
        <div 
          className={`w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center ${
            contentType === 'application/pdf' && url && !isUploading ? 'cursor-pointer hover:bg-muted/80 transition-colors' : ''
          }`}
          onClick={handlePdfClick}
        >
          {contentType ? (
            contentType.startsWith('image') ? (
              // NOTE: it is recommended to use next/image for images
              // eslint-disable-next-line @next/next/no-img-element
              <img
                key={url}
                src={url}
                alt={name ?? 'An image attachment'}
                className="rounded-md size-full object-cover"
              />
            ) : contentType === 'application/pdf' ? (
              <div className="flex flex-col items-center justify-center size-full bg-white border-2 border-dashed border-zinc-300 rounded-md hover:border-zinc-400 transition-colors">
                <div className="flex flex-col items-center justify-center space-y-1">
                  <div className="p-1.5 bg-zinc-100 rounded-md">
                    <FileIcon size={16} />
                  </div>
                  <span className="text-[9px] text-zinc-600 font-medium uppercase tracking-wider">PDF</span>
                </div>
              </div>
            ) : (
              <div className="" />
            )
          ) : (
            <div className="" />
          )}

          {isUploading && (
            <div
              data-testid="input-attachment-loader"
              className="animate-spin absolute text-zinc-500 bg-white/90 rounded-full p-1 shadow-sm"
            >
              <LoaderIcon />
            </div>
          )}
        </div>
        <div className="text-xs text-zinc-500 font-medium text-center break-words">{fileName}</div>
      </div>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent 
          side="right" 
          className="size-full sm:w-[90vw] sm:max-w-4xl p-0 border-l-0"
        >
          <div className="flex flex-col h-full">
            <SheetHeader className="px-6 py-4 border-b">
              <SheetTitle>PDF Preview</SheetTitle>
              <SheetDescription>{actualFileName}</SheetDescription>
            </SheetHeader>
            
            <div className="flex-1 bg-zinc-50">
              {url && (
                <iframe
                  src={url}
                  className="size-full border-0"
                  title={`PDF Preview: ${actualFileName}`}
                />
              )}
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};
