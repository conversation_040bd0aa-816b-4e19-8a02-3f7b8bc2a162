import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

import { MessageIcon, VercelIcon } from './icons';

export const Overview = () => {
  return (
    <motion.div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.5 }}
    >
      <div className="rounded-xl p-6 flex flex-col leading-relaxed text-left max-w-xl" style={{ gap: '1rem' }}>
        
        <Image 
          src="/meet-george-logo.png"
          alt="Meet George Logo"
          width={200}
          height={60}
          className="mx-auto mb-6"
        />
        
        <Image 
          src="/images/Cropped-George-1024x1024.jpg"
          alt="AI Assistant"
          width={128}
          height={128}
          className="rounded-full mx-auto mb-4"
        />
        <b>
          Hey there! 👋 I&apos;m <span style={{ color: '#FE6232' }}>George</span>, your personal energy comparison assistant! ✨⚡
        </b>
        <b>
          I&apos;m here to help you find a cheaper UK home energy supplier faster than you can say &quot;power surge&quot;! 
        </b>
        <b>
          Just upload a recent home energy bill and I&apos;ll extract the necessary information from it.📄👀
        </b>
        <p className="text-sm">
          (By uploading your energy bill, you agree to our{' '}
          <Link
            className="font-medium underline underline-offset-4"
            href="https://meetgeorge.co.uk/terms-of-use"
            target="_blank"
          >
            Terms of Use
          </Link>
          .)
        </p>
      </div>
    </motion.div>
  );
};
