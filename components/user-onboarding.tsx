import {
  CheckCircle2Icon,
  XCircleIcon,
  Loader2,
  UserPlus,
  ChevronDownIcon,
  ChevronRightIcon,
  ExternalLinkIcon,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface UserOnboardingToolResultProps {
  result?: { onboarding_url?: string; error?: string };
  loading: boolean;
}

export function UserOnboardingToolResult({
  result,
  loading = false,
}: UserOnboardingToolResultProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Determine status for display
  const getStatus = () => {
    if (loading) return 'loading';
    if (!result) return 'error';
    if (result.error) return 'error';
    if (result.onboarding_url) return 'success';
    return 'error';
  };

  const status = getStatus();

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="size-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle2Icon className="size-4 text-green-500" />;
      case 'error':
        return <XCircleIcon className="size-4 text-red-500" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'loading':
        return 'Setting up account...';
      case 'success':
        return 'Onboarding link ready';
      case 'error':
        return 'Setup failed';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.15 }}
      className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-950"
    >
      {/* Compact Header - Always Visible */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center gap-3 px-3 py-2.5 hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors"
      >
        <UserPlus className="size-4 text-gray-500 dark:text-gray-400" />
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            User Onboarding
          </span>
          {getStatusIcon()}
        </div>
        <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
          {getStatusText()}
        </span>
        {isExpanded ? (
          <ChevronDownIcon className="size-4 text-gray-400 shrink-0" />
        ) : (
          <ChevronRightIcon className="size-4 text-gray-400 shrink-0" />
        )}
      </button>

      {/* Expandable Details */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.15, ease: 'easeInOut' }}
            className="overflow-hidden border-t border-gray-100 dark:border-gray-800"
          >
            <div className="p-3">
              {status === 'loading' && (
                <div className="flex items-start gap-3">
                  <Loader2 className="size-4 animate-spin text-blue-500 mt-0.5 shrink-0" />
                  <div className="flex flex-col gap-1 min-w-0">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Processing onboarding...
                    </span>
                    <span className="text-xs text-gray-600 dark:text-gray-300">
                      This may take a few moments
                    </span>
                  </div>
                </div>
              )}

              {status === 'success' && result?.onboarding_url && (
                <div className="flex items-start gap-3">
                  <CheckCircle2Icon className="size-4 text-green-500 mt-0.5 shrink-0" />
                  <div className="flex flex-col gap-2 min-w-0">
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      Onboarding Link Ready
                    </span>
                    <span className="text-xs text-gray-600 dark:text-gray-300">
                      Your onboarding link has been generated successfully.
                      Click the link below to complete your account setup.
                    </span>
                    <a
                      href={result.onboarding_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors underline underline-offset-2"
                    >
                      <span>Complete Onboarding</span>
                      <ExternalLinkIcon className="size-3" />
                    </a>
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      This link will open in a new tab for security purposes
                    </span>
                  </div>
                </div>
              )}

              {status === 'error' && (
                <div className="flex items-start gap-3">
                  <XCircleIcon className="size-4 text-red-500 mt-0.5 shrink-0" />
                  <div className="flex flex-col gap-1 min-w-0">
                    <span className="text-sm font-medium text-red-600 dark:text-red-400">
                      Onboarding Setup Failed
                    </span>
                    <span className="text-xs text-gray-600 dark:text-gray-300">
                      {result?.error ||
                        'Unable to generate onboarding link. Please try again.'}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Contact support if this issue continues
                    </span>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
