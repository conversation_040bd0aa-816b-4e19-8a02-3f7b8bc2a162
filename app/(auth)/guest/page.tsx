'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { createGuestUserAndLogin } from '../actions';

export default function Page() {
  const router = useRouter();

  useEffect(() => {
    createGuestUserAndLogin().then(() => {
      router.push('/');
    });
  }, [router]);

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-12 flex flex-col">
        <div className="flex justify-center items-center">
          <div className="loader"></div>
        </div>
      </div>
    </div>
  );
}
