'use server';

import { generateText, Message } from 'ai';
import { cookies } from 'next/headers';

import {
  deleteMessagesByChatIdAfterTimestamp,
  getMessageById,
  updateChatVisiblityById,
} from '@/lib/db/queries';
import { VisibilityType } from '@/components/visibility-selector';
import { myProvider } from '@/lib/ai/providers';

export async function saveChatModelAsCookie(model: string) {
  const cookieStore = await cookies();
  cookieStore.set('chat-model', model);
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: Message;
}) {

  const { text: title } = await generateText({
    model: myProvider.languageModel('title-model'),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
    prompt: JSON.stringify(message),
  });
  
  // console.log('Creating title from user message content...');
  // console.time('Total title generation time');
  
  // // Extract message text
  // let messageText = '';
  
  // // Handle different message formats
  // if (typeof message.content === 'string') {
  //   // Handle string content
  //   messageText = message.content;
  // } else if (Array.isArray(message.content)) {
  //   // Handle array content (older format)
  //   for (const part of message.content as Array<{type: string, text: string}>) {
  //     if (part.type === 'text') {
  //       messageText = part.text;
  //       break;
  //     }
  //   }
  // } 
  
  // // Handle parts array (newer format)
  // if (!messageText && message.parts) {
  //   for (const part of message.parts) {
  //     if (typeof part === 'string') {
  //       messageText = part;
  //       break;
  //     } else if (part && typeof part === 'object' && 'type' in part && part.type === 'text' && 'text' in part) {
  //       messageText = part.text;
  //       break;
  //     }
  //   }
  // }
  
  // // Create title from the first few words (max 6 words or 50 characters)
  // let title = messageText
  //   .split(' ')
  //   .slice(0, 6)
  //   .join(' ')
  //   .trim();
    
  // // Handle empty title
  // if (!title) {
  //   title = 'New Chat';
  // }
  
  // // Limit title length to 50 chars
  // if (title.length > 50) {
  //   title = title.substring(0, 47) + '...';
  // }
  
  // // Add ellipsis if title was truncated
  // if (title.length < messageText.length && !title.endsWith('...')) {
  //   title += '...';
  // }
  
  // console.timeEnd('Total title generation time');
  // console.log('Title created:', title);
  
  return title;
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  const [message] = await getMessageById({ id });

  await deleteMessagesByChatIdAfterTimestamp({
    chatId: message.chatId,
    timestamp: message.createdAt,
  });
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  await updateChatVisiblityById({ chatId, visibility });
}
