import { NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { config } from 'dotenv';
import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv';
import { createClient } from '@supabase/supabase-js';

// Rate limiting configuration
const MAX_UPLOADS_PER_DAY = 20;

const ratelimit = new Ratelimit({
  redis: kv,
  limiter: Ratelimit.slidingWindow(MAX_UPLOADS_PER_DAY, '24 h'),
});

if (process.env.NODE_ENV !== 'production') {
  config({
    path: '.env.local',
  });
}

// Verify essential environment variables are present
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.error(`Missing required environment variable: ${envVar}`);
    // Continue execution but log the error
  }
}

// Initialize Supabase client with service role for server-side operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

const FileSchema = z.object({
  file: z
    .instanceof(Blob)
    .refine((file) => file.size <= 5 * 1024 * 1024, {
      message: 'File size should be less than 5MB',
    })
    .refine(
      (file) =>
        ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type),
      {
        message: 'File type should be JPEG, PNG, or PDF',
      },
    ),
});

export async function POST(request: Request) {
  const session = await auth();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = session.user?.id;
  
  if (!userId) {
    return NextResponse.json({ error: 'User ID not found' }, { status: 400 });
  }
  
  // Check rate limits (using userId as the identifier)
  const { success, remaining } = await ratelimit.limit(userId);
  console.log(`Ratelimit remaining: ${remaining} for user: ${userId}`);
  
  if (!success) {
    console.log(`Ratelimited! for user: ${userId}`);
    return NextResponse.json({ 
      error: `Rate limit exceeded. Maximum ${MAX_UPLOADS_PER_DAY} uploads per day allowed.` 
    }, { status: 429 });
  }

  if (request.body === null) {
    return new Response('Request body is empty', { status: 400 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('file') as Blob;

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    const validatedFile = FileSchema.safeParse({ file });

    if (!validatedFile.success) {
      const errorMessage = validatedFile.error.errors
        .map((error) => error.message)
        .join(', ');

      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Get filename and prepare for upload
    const originalFilename = (formData.get('file') as File).name;
    
    // Generate a unique filename to prevent overwrites
    const uniqueFilename = `${Date.now()}-${originalFilename}`;
    const filePath = `uploads/${userId}/${uniqueFilename}`;

    try {
      // Convert Blob to ArrayBuffer for Supabase upload
      const arrayBuffer = await file.arrayBuffer();

      // Upload file to Supabase storage
      const { data, error } = await supabase.storage
        .from('user-bills')
        .upload(filePath, arrayBuffer, {
          contentType: file.type,
          duplex: 'half'
        });

      if (error) {
        console.error('Supabase upload error:', error);
        return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 });
      }

      // Generate a signed URL for downloading/viewing (valid for 24 hours)
      const { data: urlData, error: urlError } = await supabase.storage
        .from('user-bills')
        .createSignedUrl(filePath, 86400); // 24 hours

      if (urlError) {
        console.error('Failed to generate signed URL:', urlError);
        return NextResponse.json({ error: 'Failed to generate download URL' }, { status: 500 });
      }

      // Log the upload for audit purposes
      console.log(`File uploaded by ${userId}: ${uniqueFilename}`);
      
      // Store in KV with 24-hour expiration
      const key = `bill_url:${userId}`;
      await kv.set(key, filePath, { ex: 86400 });
      console.log(`Stored bill URL for user ${userId}: ${filePath}`);

      return NextResponse.json({
        url: urlData.signedUrl,
        downloadUrl: urlData.signedUrl,
        pathname: filePath,
        contentType: file.type,
        contentDisposition: `inline; filename="${encodeURIComponent(originalFilename)}"`
      });
    } catch (error) {
      console.error('File upload processing error:', error);
      return NextResponse.json({ error: 'Failed to process file upload' }, { status: 500 });
    }
  } catch (error) {
    console.error('Request processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 },
    );
  }
}
