import {
  UIMessage,
  appendResponseMessages,
  createDataStreamResponse,
  smoothStream,
  streamText,
  tool,
} from 'ai';
import { auth } from '@/app/(auth)/auth';
import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv';
import { systemPrompt } from '@/lib/ai/prompts';
import {
  deleteChatById,
  getChatById,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import {
  generateUUID,
  getMostRecentUserMessage,
  getTrailingMessageId,
} from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { userOnboarding } from '@/lib/ai/tools/user-onboarding';
import { sendEmailVerification } from '@/lib/ai/tools/send-email-verification';
import { verifyEmailVerificationCode } from '@/lib/ai/tools/verify-email-verification-code';
import { myProvider } from '@/lib/ai/providers';
import { isProductionEnvironment } from '@/lib/constants';
import { z } from 'zod';

export const maxDuration = 60;

// Create Rate limit
const ratelimit = new Ratelimit({
  redis: kv,
  limiter: Ratelimit.fixedWindow(50, '24h'),
});

export async function POST(request: Request) {
  // Create AbortController to potentially terminate operations if rate limited
  const abortController = new AbortController();
  const signal = abortController.signal;

  // Only apply rate limiting in production
  if (isProductionEnvironment) {
    console.time('Ratelimit check');
    try {
      // call ratelimit with request ip
      const ip = request.headers.get('x-forwarded-for') ?? 'ip';
      const { success, remaining, limit, reset } = await ratelimit.limit(ip);
      console.timeEnd('Ratelimit check');

      console.log(`Ratelimit remaining: ${remaining} for ip: ${ip}`);

      // If rate limited, abort ongoing operations
      if (!success) {
        console.log(`Ratelimited! for ip: ${ip}`);
        // abortController.abort('Rate limit exceeded');
        // return false;
        const response = createDataStreamResponse({
          execute: async (dataStream) => {
            dataStream.writeMessageAnnotation({
              type: 'status',
              value: 'rate-limited',
            });
          },
        });
        return response;
      }
      // return true;
    } catch (error) {
      console.error('Rate limit check failed:', error);
      // On error, allow the request to proceed
      // continue
    }
  }

  try {
    const {
      id,
      messages,
      selectedChatModel,
    }: {
      id: string;
      messages: Array<UIMessage>;
      selectedChatModel: string;
    } = await request.json();

    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const userMessage = getMostRecentUserMessage(messages);

    if (!userMessage) {
      return new Response('No user message found', { status: 400 });
    }

    // Check for rate limit before returning response
    // const rateLimitResult = await Promise.race([
    //   rateLimitPromise,
    //   // Set a timeout to continue anyway if rate limit check takes too long
    //   new Promise<true>(resolve => setTimeout(() => resolve(true), 1500))
    // ]);

    // if (!rateLimitResult) {
    //   return new Response('You have exceeded the rate limit. Please try again later.', { status: 429 });
    // }

    // Start DB operations asynchronously
    const dbOperationsPromise = (async () => {
      console.time('Database operations');
      try {
        const chat = await getChatById({ id });

        // Ensure session.user.id is a valid string
        const userId = session.user?.id;
        if (!userId) {
          throw new Error('Invalid user ID');
        }

        if (!chat) {
          const title = await generateTitleFromUserMessage({
            message: userMessage,
          });

          await saveChat({ id, userId, title });
        } else {
          if (chat.userId !== userId) {
            throw new Error('Unauthorized');
          }
        }

        await saveMessages({
          messages: [
            {
              chatId: id,
              id: userMessage.id,
              role: 'user',
              parts: userMessage.parts,
              attachments: userMessage.experimental_attachments ?? [],
              createdAt: new Date(),
            },
          ],
        });
      } finally {
        console.timeEnd('Database operations');
      }
    })();

    // Setup stream response in parallel with DB operations
    const response = createDataStreamResponse({
      execute: async (dataStream) => {
        // No need to await dbOperationsPromise here - let it run in parallel
        // Just handle any errors that might occur
        dbOperationsPromise.catch((error) => {
          console.error('Database operations failed:', error);
          // Don't throw here as it would stop the streaming
        });

        const result = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: systemPrompt({
            selectedChatModel,
            sessionUserId: session.user?.id ?? '',
          }),
          messages,
          maxSteps: 10,
          abortSignal: signal,
          experimental_activeTools:
            selectedChatModel === 'chat-model-reasoning'
              ? []
              : [
                  'userOnboarding',
                  'sendEmailVerification',
                  'verifyEmailVerificationCode',
                ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          tools: {
            userOnboarding,
            sendEmailVerification,
            verifyEmailVerificationCode,
          },
          onFinish: async ({ response }) => {
            console.time('Save assistant message');
            if (session.user?.id) {
              try {
                // Wait for the initial DB operations to complete before saving the assistant message
                await dbOperationsPromise;

                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (message) => message.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [userMessage],
                  responseMessages: response.messages,
                });

                let parts = assistantMessage.parts;

                // Associate confirmation request with the specific assistant message ID
                if (
                  userMessage.experimental_attachments &&
                  userMessage.experimental_attachments.length > 0
                ) {
                  parts?.push({
                    type: 'tool-invocation',
                    toolInvocation: {
                      toolName: 'ask-for-confirmation',
                      toolCallId: generateUUID(),
                      state: 'call',
                      args: JSON.stringify({
                        messageId: assistantId,
                      }),
                    },
                  });
                  dataStream.writeMessageAnnotation({
                    type: 'ask-for-confirmation',
                    messageId: assistantId,
                  });
                }

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      role: assistantMessage.role,
                      parts: parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                    },
                  ],
                });
              } catch (_) {
                console.error('Failed to save chat');
              }
            }
            console.timeEnd('Save assistant message');
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
          onError: (error) => {
            console.error('Streaming Error:', error);
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        return 'Oops, an error occurred!';
      },
    });
    return response;
  } catch (error) {
    return new Response('An error occurred while processing your request!', {
      status: 404,
    });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (chat.userId !== session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    await deleteChatById({ id });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
