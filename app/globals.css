@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #ededed;
  border-radius: 8px;
  border: 2px solid #f7f8fc;
}

::-webkit-scrollbar-track {
  background: #f7f8fc;
  border-radius: 8px;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #ededed #f7f8fc;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 228 56% 98%; /* #f7f8fc */
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 225 14% 82% / 0.3; /* rgba(202, 204, 217, .3) */
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Sidebar hover styles */
[data-sidebar='menu-button']:hover {
  background: hsl(var(--sidebar-accent)) !important;
}

[data-sidebar='menu-action']:hover {
  background: hsl(var(--sidebar-accent)) !important;
}

[data-sidebar='group-action']:hover {
  background: hsl(var(--sidebar-accent)) !important;
}

[data-sidebar='menu-sub-button']:hover {
  background: hsl(var(--sidebar-accent)) !important;
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^='text-'] {
    color: transparent;
    @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent !important;
}

.cm-activeLine {
  @apply rounded-r-sm !important;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm !important;
}

.suggestion-highlight {
  @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}

.loader {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s ease infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Mobile overflow fixes */
html,
body {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

/* Mobile-specific fixes */
@media (max-width: 767px) {
  /* Ensure main containers don't exceed viewport */
  .container,
  main,
  [role='main'],
  [data-sidebar='sidebar'] {
    max-width: 100vw !important;
    overflow-x: hidden;
  }

  /* Fix artifact overlay on mobile */
  [data-testid='artifact'] {
    max-width: 100vw !important;
    overflow-x: hidden;
  }

  /* Prevent code blocks from causing horizontal scroll */
  pre,
  code,
  .code-editor {
    max-width: 100% !important;
    overflow-x: auto;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  /* Fix console component */
  .console-container,
  [style*='height'] {
    max-width: 100vw !important;
    overflow-x: hidden;
  }

  /* Ensure messages don't overflow */
  .message-container,
  [class*='flex-col'],
  [class*='gap-'] {
    max-width: calc(100vw - 1rem) !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Fix sheet content */
  .sheet-content {
    max-width: 100vw !important;
  }

  /* Fix any fixed positioning elements */
  .fixed {
    max-width: 100vw !important;
  }

  /* Prevent tables from overflowing */
  table {
    max-width: 100% !important;
    table-layout: fixed;
  }

  /* Fix input and textarea elements */
  input,
  textarea {
    max-width: 100% !important;
  }
}
