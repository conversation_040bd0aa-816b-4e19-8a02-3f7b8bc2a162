import { openai } from '@ai-sdk/openai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { createVertex } from '@ai-sdk/google-vertex';

// import { wrapLanguageModel, extractReasoningMiddleware } from 'ai';
// import { customMiddleware } from './custom-middleware';

const vertex = createVertex();

// const openrouter = createOpenRouter({
//   apiKey: process.env.OPENROUTER_API_KEY,
// });

import { createAmazonBedrock } from '@ai-sdk/amazon-bedrock';

const bedrock = createAmazonBedrock({
  region: process.env.BEDROCK_AWS_REGION,
  accessKeyId: process.env.BEDROCK_ACCESS_KEY_ID,
  secretAccessKey: process.env.BEDROCK_SECRET_ACCESS_KEY,
  sessionToken: undefined
});

const anthropic = createAnthropic({
  apiKey: process.env.CLAUDE_API_KEY,
});

// const google = createGoogleGenerativeAI({
//   apiKey: process.env.GEMINI_API_KEY,
// });

// This works
// google("gemini-2.0-pro-exp-02-05", { structuredOutputs: true })

export const customModel = (apiIdentifier: string) => {
  // this works return google("gemini-1.5-flash")
  // return vertex("gemini-2.0-flash-001")
  // return vertex("claude-3-7-sonnet@20250219")
  return anthropic("claude-3-5-sonnet-latest")
  // return bedrock("anthropic.claude-3-5-sonnet-20241022-v2:0")
};

export const imageGenerationModel = openai.image('dall-e-3');
