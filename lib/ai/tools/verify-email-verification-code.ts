import { tool } from 'ai';
import { z } from 'zod';

export const verifyEmailVerificationCode = tool({
  description: 'Verify a verification code for a user\'s email address',
  parameters: z.object({
    email: z.string(),
    code: z.string(),
  }),
  execute: async ({ email, code }) => {
    console.log('Verifying code for email', email);

    // Create an AbortController with a timeout of 10 seconds
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    try {
      const response = await fetch(
        `${process.env.SERVER_HOST}/api/v1/onboarding/verify_code`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.SERVER_API_KEY}`
          },
          body: JSON.stringify({
            verification: {
              email,
              code: code
            }
          }),
          signal: controller.signal
        }
      );

      // Clear the timeout as the request completed
      clearTimeout(timeoutId);

      // Check response status - only 200 and 201 are considered success
      if (response.status === 200 || response.status === 201) {
        const result = await response.json();
        console.log('Code verification result for', email, result);
        
        // Add success flag if not present
        return { ...result, success: true };
      } else {
        // Handle non-successful status codes (4XX and 5XX)
        console.error(`Server returned non-success status ${response.status} ${response.statusText}`);
        return {
          error: `Server returned non-success status ${response.status} ${response.statusText}`,
          success: false
        };
      }
    } catch (error) {
      // Clear the timeout if there was an error
      clearTimeout(timeoutId);

      console.error('Error in code verification:', error);
      
      // Specific error for timeout
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          error: 'Verification request timed out. Please try again later.',
          success: false
        };
      }
      
      // General error response
      return {
        error: 'Failed to verify code. Please try again later.',
        success: false
      };
    } finally {
      // Make sure timeout is cleared in all cases
      clearTimeout(timeoutId);
    }
  },
}); 