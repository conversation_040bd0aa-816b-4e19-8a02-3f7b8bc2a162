import { tool } from 'ai';
import { z } from 'zod';
import { kv } from '@vercel/kv';

export const userOnboarding = tool({
  description: 'Submits extracted energy bill data and user email to the database.  Returns a URL for energy offers.',
  parameters: z.object({
    personal: z.object({
      email: z.string(),
      first_name: z.string(),
      last_name: z.string()
    }),
    address: z.object({
      full_address: z.string(),
      postcode: z.string(),
      posttown: z.string()
    }),
    bill: z.object({
      account_number: z.string(),
      bill_date: z.string(),
      bill_reference: z.string(),
      billing_period: z.string(),
      amount_payable: z.number(),
      percentage_of_vat_applied: z.number()
    }),
    supplier: z.object({
      name: z.string(),
      tariff_name: z.string()
    }),
    fuel_type: z.enum(['gas', 'electricity', 'both']),
    electricity: z.object({
      meter_serial_number: z.string(),
      unit_rate: z.number(),
      standing_charge: z.number(),
      estimated_annual_usage: z.number(),
      estimated_annual_cost: z.number(),
      monthly_usage: z.number(),
      payment_method: z.string(),
      product_type: z.string(),
      early_exit_fee: z.number()
    }).optional(),
    gas: z.object({
      meter_serial_number: z.string(),
      meter_point_reference_number: z.string(),
      unit_rate: z.number(),
      standing_charge: z.number(),
      estimated_annual_usage: z.number(),
      estimated_annual_cost: z.number(),
      monthly_usage: z.number(),
      payment_method: z.string(),
      product_type: z.string(),
      early_exit_fee: z.number()
    }).optional(),
    session_user_id: z.string()
  }),
  execute: async ({ 
    personal,
    address,
    bill,
    supplier,
    fuel_type,
    electricity,
    gas,
    session_user_id
  }, { abortSignal }) => {
    console.log('Submitting user data to generate energy offers');

    // Get bill_url from KV storage using chat_id
    const bill_url = await kv.get(`bill_url:${session_user_id}`) || "";

    const params = {
      onboarding: {
        personal: {
          email: personal.email,
          first_name: personal.first_name,
          last_name: personal.last_name
        },
        address: {
          full_address: address.full_address,
          postcode: address.postcode,
          posttown: address.posttown
        },
        bill: {
          account_number: bill.account_number,
          bill_date: bill.bill_date,
          bill_reference: bill.bill_reference,
          billing_period: bill.billing_period,
          amount_payable: bill.amount_payable,
          percentage_of_vat_applied: bill.percentage_of_vat_applied,
          bill_url: bill_url
        },
        supplier: {
          name: supplier.name,
          tariff_name: supplier.tariff_name
        },
        fuel_type,
        electricity: electricity ? {
          meter_serial_number: electricity.meter_serial_number,
          unit_rate: electricity.unit_rate,
          standing_charge: electricity.standing_charge,
          estimated_annual_usage: electricity.estimated_annual_usage,
          estimated_annual_cost: electricity.estimated_annual_cost,
          monthly_usage: electricity.monthly_usage,
          payment_method: electricity.payment_method,
          product_type: electricity.product_type,
          early_exit_fee: electricity.early_exit_fee,
        } : null,
        gas: gas ? {
          meter_serial_number: gas.meter_serial_number,
          meter_point_reference_number: gas.meter_point_reference_number,
          unit_rate: gas.unit_rate,
          standing_charge: gas.standing_charge,
          estimated_annual_usage: gas.estimated_annual_usage,
          estimated_annual_cost: gas.estimated_annual_cost,
          monthly_usage: gas.monthly_usage,
          payment_method: gas.payment_method,
          product_type: gas.product_type,
          early_exit_fee: gas.early_exit_fee
        } : null
      }
    }

    const response = await fetch(
      `${process.env.SERVER_HOST}/api/v1/onboarding`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SERVER_API_KEY}`
        },
        body: JSON.stringify(params),
        signal: abortSignal
      }
    );

    const result = await response.json();

    return result;
  },
});