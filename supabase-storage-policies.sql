-- Storage policies for 'user-bills' bucket
-- These policies ensure only authenticated users can upload and access their own files

-- Enable RLS on the storage.objects table (if not already enabled)
-- This is usually done automatically when you create policies, but just to be sure:
-- ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy 1: Allow authenticated users to upload files to their own folder
CREATE POLICY "Users can upload their own bills" ON storage.objects
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'user-bills' 
    AND auth.uid()::text = (storage.foldername(name))[1]
    AND (storage.foldername(name))[1] = 'uploads'
    AND (storage.foldername(name))[2] = auth.uid()::text
  );

-- Policy 2: Allow authenticated users to view/download their own files
CREATE POLICY "Users can view their own bills" ON storage.objects
  FOR SELECT 
  USING (
    bucket_id = 'user-bills' 
    AND auth.uid()::text = (storage.foldername(name))[2]
    AND (storage.foldername(name))[1] = 'uploads'
  );

-- Policy 3: Allow authenticated users to update their own files (optional)
CREATE POLICY "Users can update their own bills" ON storage.objects
  FOR UPDATE 
  USING (
    bucket_id = 'user-bills' 
    AND auth.uid()::text = (storage.foldername(name))[2]
    AND (storage.foldername(name))[1] = 'uploads'
  );

-- Policy 4: Allow authenticated users to delete their own files (optional)
CREATE POLICY "Users can delete their own bills" ON storage.objects
  FOR DELETE 
  USING (
    bucket_id = 'user-bills' 
    AND auth.uid()::text = (storage.foldername(name))[2]
    AND (storage.foldername(name))[1] = 'uploads'
  );

-- Alternative simpler approach using path pattern matching:
-- If the above doesn't work, you can try these simplified policies:

/*
-- Simple upload policy
CREATE POLICY "Users can upload to their folder" ON storage.objects
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'user-bills' 
    AND (storage.foldername(name))[1] = 'uploads'
    AND (storage.foldername(name))[2] = auth.uid()::text
  );

-- Simple select policy  
CREATE POLICY "Users can view their folder" ON storage.objects
  FOR SELECT 
  USING (
    bucket_id = 'user-bills' 
    AND (storage.foldername(name))[1] = 'uploads'
    AND (storage.foldername(name))[2] = auth.uid()::text
  );
*/ 